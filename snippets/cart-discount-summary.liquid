{% comment %}
  Renders compact discount summary in cart totals
  
  Usage:
  {% render 'cart-discount-summary' %}
{% endcomment %}

{%- if settings.dynamic_campaigns_enabled -%}
  {{ 'component-cart-discount-summary.css' | asset_url | stylesheet_tag }}
  
  <div class="cart-discount-summary" id="cart-discount-summary">
    <!-- Discounts will be populated by JavaScript -->
  </div>

  <script>
    // Pass theme settings to JavaScript for discount calculation
    window.themeSettings = window.themeSettings || {};
    window.themeSettings.dynamic_campaigns_enabled = {{ settings.dynamic_campaigns_enabled | json }};
    window.themeSettings.cart_campaigns = {{ settings.cart_campaigns | json }};
    window.themeSettings.product_campaigns = {{ settings.product_campaigns | json }};
    
    console.log('Cart discount summary loaded');
  </script>

  <script src="{{ 'cart-discount-summary.js' | asset_url }}" defer="defer"></script>
{%- endif -%}
