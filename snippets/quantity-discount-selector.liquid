{% comment %}
  Renders quantity discount selector.
  Accepts:
  - block: {Object} block object.
  - product: {Object} product object.
  - section_id: {String} id of section to which this snippet belongs.

  Usage:
  {% render 'quantity-discount-selector', block: block, product: product, section_id: section.id %}
{% endcomment %}

<!-- DEBUG: Snippet called -->
<!-- DEBUG: block.settings.discount_tiers = {{ block.settings.discount_tiers }} -->

{%- comment -%} Force correct values for testing {%- endcomment -%}
{%- assign discount_tiers = "2|1080|2'li Paket - %10 İndirim
3|1440|3'lü Paket - %15 İndirim
5|2000|5'li Paket - %20 İndirim" -%}

<!-- DEBUG: Final discount_tiers = {{ discount_tiers }} -->

{%- if discount_tiers != blank or true -%}
  {{ 'component-quantity-discount.css' | asset_url | stylesheet_tag }}

  <div class="quantity-discount-wrapper" {{ block.shopify_attributes }}>
    {%- assign heading = block.settings.heading -%}
    {%- if heading == blank and settings.quantity_discount_enabled -%}
      {%- assign heading = settings.quantity_discount_default_heading -%}
    {%- endif -%}
    {%- if heading != blank -%}
      <h3 class="quantity-discount__heading">{{ heading }}</h3>
    {%- endif -%}

    {%- assign discount_lines = discount_tiers | newline_to_br | split: '<br />' -%}
    {%- assign current_price = product.selected_or_first_available_variant.price -%}
    
    <div class="quantity-discount__options">
      {%- for line in discount_lines -%}
        {%- unless line == blank -%}
          {%- assign parts = line | split: '|' -%}
          {%- if parts.size >= 3 -%}
            {%- assign quantity = parts[0] | strip | plus: 0 -%}
            {%- assign discount_price_tl = parts[1] | strip | plus: 0 -%}
            {%- assign label = parts[2] | strip -%}

            <!-- DEBUG: quantity={{ quantity }}, discount_price_tl={{ discount_price_tl }}, label={{ label }} -->
            <!-- DEBUG: current_price={{ current_price }} -->

            {%- comment -%}
              Settings contain TL values (e.g., 1080 = 1080 TL)
              Convert to cents for Shopify money calculations (1080 TL = 108000 cents)
            {%- endcomment -%}
            {%- assign discount_price_cents = discount_price_tl | times: 100 -%}
            {%- assign total_regular_price = current_price | times: quantity -%}
            {%- assign savings = total_regular_price | minus: discount_price_cents -%}
            {%- assign savings_percentage = savings | times: 100.0 | divided_by: total_regular_price | round -%}

            <!-- DEBUG: discount_price_cents={{ discount_price_cents }}, total_regular_price={{ total_regular_price }}, savings={{ savings }} -->
            
            <div class="quantity-discount__option"
                 data-quantity="{{ quantity }}"
                 data-price="{{ discount_price_cents }}"
                 data-regular-price="{{ total_regular_price }}"
                 data-savings="{{ savings }}"
                 data-savings-percentage="{{ savings_percentage }}"
                 data-variant-id="{{ product.selected_or_first_available_variant.id }}">
              <div class="quantity-discount__content">
                <div class="quantity-discount__label">{{ label }}</div>
                <div class="quantity-discount__pricing">
                  <span class="quantity-discount__price">{{ discount_price_cents | money }}</span>
                  {%- assign show_savings = block.settings.show_savings -%}
                  {%- if show_savings == blank and settings.quantity_discount_enabled -%}
                    {%- assign show_savings = settings.quantity_discount_show_savings -%}
                  {%- endif -%}
                  {%- if show_savings and savings > 0 -%}
                    <span class="quantity-discount__regular-price">{{ total_regular_price | money }}</span>
                    <span class="quantity-discount__savings">{{ savings | money }} tasarruf</span>
                  {%- endif -%}
                </div>
                <div class="quantity-discount__per-item">
                  Adet başı: {{ discount_price_cents | divided_by: quantity | money }}
                </div>
              </div>
              <button type="button"
                      class="quantity-discount__button button button--secondary"
                      data-quantity="{{ quantity }}"
                      data-variant-id="{{ product.selected_or_first_available_variant.id }}">
                Sepete Ekle
              </button>
            </div>
          {%- endif -%}
        {%- endunless -%}
      {%- endfor -%}
    </div>
  </div>

  <script src="{{ 'quantity-discount.js' | asset_url }}" defer="defer"></script>
{%- endif -%}
