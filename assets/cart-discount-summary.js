class CartDiscountSummary {
  constructor() {
    this.campaigns = [];
    this.cartData = null;
    this.init();
  }

  init() {
    this.loadSettings();
    this.bindEvents();
    this.updateDiscounts();
  }

  loadSettings() {
    this.settings = {
      enabled: window.themeSettings && window.themeSettings.dynamic_campaigns_enabled !== false,
      cartCampaigns: (window.themeSettings && window.themeSettings.cart_campaigns) || '',
      productCampaigns: (window.themeSettings && window.themeSettings.product_campaigns) || ''
    };
    
    console.log('Cart discount summary settings loaded:', this.settings);
  }

  bindEvents() {
    // Listen for cart updates
    document.addEventListener('cart:updated', () => {
      this.updateDiscounts();
    });

    // Listen for cart changes via fetch
    const originalFetch = window.fetch;
    window.fetch = (...args) => {
      const result = originalFetch.apply(this, args);
      
      if (args[0] && (
        args[0].includes('/cart/add') || 
        args[0].includes('/cart/change') || 
        args[0].includes('/cart/update')
      )) {
        result.then(() => {
          setTimeout(() => this.updateDiscounts(), 200);
        });
      }
      
      return result;
    };
  }

  async updateDiscounts() {
    if (!this.settings.enabled) return;

    try {
      await this.fetchCartData();
      this.parseCampaigns();
      this.calculateActiveDiscounts();
      this.renderDiscounts();
    } catch (error) {
      console.error('Error updating cart discounts:', error);
    }
  }

  async fetchCartData() {
    try {
      const response = await fetch('/cart.js');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      this.cartData = await response.json();
    } catch (error) {
      console.error('Error fetching cart data:', error);
      this.cartData = {
        items: [],
        item_count: 0,
        total_price: 0
      };
    }
  }

  parseCampaigns() {
    this.campaigns = [];

    // Parse cart campaigns
    if (this.settings.cartCampaigns) {
      const cartLines = this.settings.cartCampaigns.split('\n');
      cartLines.forEach(line => {
        const campaign = this.parseCartCampaign(line.trim());
        if (campaign) this.campaigns.push(campaign);
      });
    }
  }

  parseCartCampaign(line) {
    if (!line || line.startsWith('#')) return null;
    
    const parts = line.split('|');
    if (parts.length < 5) return null;

    return {
      type: 'cart',
      campaignType: parts[0].trim(),
      condition: parseFloat(parts[1].trim()),
      value: parseFloat(parts[2].trim()),
      title: parts[3].trim(),
      description: parts[4].trim(),
      active: false,
      discount: 0
    };
  }

  calculateActiveDiscounts() {
    if (!this.cartData) return;

    const cartTotal = this.cartData.total_price / 100; // Convert from cents
    const cartQuantity = this.cartData.item_count;

    this.campaigns.forEach(campaign => {
      switch (campaign.campaignType) {
        case 'amount_threshold':
          campaign.active = cartTotal >= campaign.condition;
          campaign.discount = campaign.active ? campaign.value : 0;
          break;

        case 'quantity_threshold':
          campaign.active = cartQuantity >= campaign.condition;
          campaign.discount = campaign.active ? campaign.value : 0;
          break;

        case 'free_shipping':
          campaign.active = cartTotal >= campaign.condition;
          campaign.discount = campaign.active ? 'Ücretsiz Kargo' : 0;
          break;

        case 'percentage_discount':
          campaign.active = cartTotal >= campaign.condition;
          campaign.discount = campaign.active ? (cartTotal * campaign.value / 100) : 0;
          break;
      }
    });
  }

  renderDiscounts() {
    const container = document.getElementById('cart-discount-summary');
    if (!container) return;

    const activeDiscounts = this.campaigns.filter(c => c.active && c.discount);
    
    if (activeDiscounts.length === 0) {
      container.innerHTML = '';
      return;
    }

    let html = '';
    let totalDiscount = 0;

    activeDiscounts.forEach(discount => {
      if (typeof discount.discount === 'number') {
        totalDiscount += discount.discount;
        html += `
          <div class="discount-line">
            <span class="discount-title">${discount.title}</span>
            <span class="discount-amount">-${this.formatMoney(discount.discount * 100)}</span>
          </div>
        `;
      } else {
        // Free shipping or other non-monetary discounts
        html += `
          <div class="discount-line">
            <span class="discount-title">${discount.title}</span>
            <span class="discount-amount">${discount.discount}</span>
          </div>
        `;
      }
    });

    // Show total discount if there are multiple discounts
    if (activeDiscounts.filter(d => typeof d.discount === 'number').length > 1) {
      html += `
        <div class="discount-total">
          <span class="discount-total-title">Toplam İndirim:</span>
          <span class="discount-total-amount">-${this.formatMoney(totalDiscount * 100)}</span>
        </div>
      `;
    }

    // Calculate and show final total
    const originalTotal = this.cartData.total_price;
    const finalTotal = originalTotal - (totalDiscount * 100);
    
    if (totalDiscount > 0) {
      html += `
        <div class="final-total">
          <span class="final-total-title">Ödenecek Tutar:</span>
          <span class="final-total-amount">${this.formatMoney(finalTotal)}</span>
        </div>
      `;
    }

    container.innerHTML = html;
  }

  formatMoney(cents) {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(cents / 100);
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  window.cartDiscountSummary = new CartDiscountSummary();
});

// Export for use in other scripts
window.CartDiscountSummary = CartDiscountSummary;
