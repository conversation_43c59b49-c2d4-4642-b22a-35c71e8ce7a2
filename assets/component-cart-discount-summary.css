.cart-discount-summary {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(var(--color-foreground), 0.1);
}

.discount-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 1.4rem;
}

.discount-title {
  color: rgba(var(--color-foreground), 0.8);
  display: flex;
  align-items: center;
}

.discount-title::before {
  content: "🎉";
  margin-right: 0.5rem;
  font-size: 1.2rem;
}

.discount-amount {
  color: #d73527;
  font-weight: 600;
}

.discount-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1rem 0 0.5rem 0;
  padding-top: 0.5rem;
  border-top: 1px solid rgba(var(--color-foreground), 0.1);
  font-size: 1.4rem;
}

.discount-total-title {
  color: rgba(var(--color-foreground), 0.9);
  font-weight: 600;
}

.discount-total-amount {
  color: #d73527;
  font-weight: 700;
  font-size: 1.5rem;
}

.final-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 2px solid rgba(var(--color-foreground), 0.2);
  font-size: 1.6rem;
}

.final-total-title {
  color: rgba(var(--color-foreground), 1);
  font-weight: 700;
}

.final-total-amount {
  color: #28a745;
  font-weight: 700;
  font-size: 1.8rem;
}

/* Cart drawer specific styles */
.cart-drawer .cart-discount-summary {
  margin-top: 0.8rem;
  padding-top: 0.8rem;
}

.cart-drawer .discount-line {
  font-size: 1.3rem;
  margin-bottom: 0.4rem;
}

.cart-drawer .discount-total {
  font-size: 1.3rem;
  margin: 0.8rem 0 0.4rem 0;
  padding-top: 0.4rem;
}

.cart-drawer .final-total {
  font-size: 1.4rem;
  margin-top: 0.8rem;
  padding-top: 0.8rem;
}

.cart-drawer .final-total-amount {
  font-size: 1.6rem;
}

/* Mobile responsive */
@media screen and (max-width: 749px) {
  .discount-line {
    font-size: 1.3rem;
  }
  
  .discount-title::before {
    font-size: 1.1rem;
  }
  
  .discount-total {
    font-size: 1.3rem;
  }
  
  .discount-total-amount {
    font-size: 1.4rem;
  }
  
  .final-total {
    font-size: 1.4rem;
  }
  
  .final-total-amount {
    font-size: 1.6rem;
  }
}

/* Animation for discount updates */
.discount-line,
.discount-total,
.final-total {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Special styling for free shipping */
.discount-line:has(.discount-amount:contains("Ücretsiz")) .discount-title::before {
  content: "🚚";
}

/* Special styling for percentage discounts */
.discount-line:has(.discount-amount:contains("%")) .discount-title::before {
  content: "🎯";
}

/* Hover effects for better UX */
.discount-line:hover {
  background: rgba(var(--color-foreground), 0.02);
  padding: 0.2rem 0.5rem;
  margin: 0.3rem -0.5rem;
  border-radius: 0.3rem;
  transition: all 0.2s ease;
}

/* Loading state */
.cart-discount-summary.loading {
  opacity: 0.6;
  pointer-events: none;
}

.cart-discount-summary.loading::after {
  content: "Hesaplanıyor...";
  display: block;
  text-align: center;
  font-size: 1.2rem;
  color: rgba(var(--color-foreground), 0.6);
  margin-top: 0.5rem;
}
